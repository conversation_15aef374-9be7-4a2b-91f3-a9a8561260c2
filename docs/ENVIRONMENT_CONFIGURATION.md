# Environment-Based Configuration Guide

## Overview

This application now supports environment-based configuration that allows tests to run with different configurations based on the target environment. This provides better flexibility for testing against different environments (local, dev, test, prod).

## Supported Environments

### 1. **Local Environment** (`local`)
- Uses LocalStack for AWS services
- Suitable for local development
- Configuration file: `application-local.properties`

### 2. **Development Environment** (`dev`) 
- Uses real AWS services with dev-specific settings
- Configuration file: `application-dev.properties`
- Environment variables prefix: `DEV_*`

### 3. **Test Environment** (`test`)
- Uses LocalStack for isolated testing
- Optimized for fast test execution
- Configuration file: `application-test.properties`

### 4. **Production Environment** (`prod`)
- Uses real AWS production services
- Configuration file: `application-prod.properties`
- Environment variables prefix: `PROD_*`

## How to Use

### Running Tests with Specific Environment

#### Method 1: Using the provided script
```bash
# Run all tests with local environment
./scripts/run-tests-with-environment.sh local

# Run all tests with dev environment
./scripts/run-tests-with-environment.sh dev

# Run specific test class with dev environment
./scripts/run-tests-with-environment.sh dev ConfigurationServiceITSpec
```

#### Method 2: Using Gradle directly
```bash
# Set environment via system property
./gradlew testAdhoc -Dtest.environment=dev

# Set environment via environment variable
TEST_ENVIRONMENT=dev ./gradlew testAdhoc

# Run with specific Spring profile
./gradlew testAdhoc -Dspring.profiles.active=dev
```

#### Method 3: Using environment variables
```bash
export TEST_ENVIRONMENT=dev
export SPRING_PROFILES_ACTIVE=dev
./gradlew testAdhoc
```

### Environment Variable Override

Each environment supports environment variable overrides:

#### Development Environment Variables
```bash
export DEV_AWS_REGION=us-west-2
export DEV_AWS_ACCESS_KEY=your-dev-access-key
export DEV_AWS_SECRET_KEY=your-dev-secret-key
export DEV_DYNAMODB_ENDPOINT=https://dynamodb.us-west-2.amazonaws.com
export DEV_S3_BUCKET_NAME=my-dev-bucket
export DEV_EVENTS_TABLE_NAME=custom-dev-Events
export DEV_BLOCK_HEIGHT_TABLE_NAME=custom-dev-BlockHeight
export DEV_TABLE_PREFIX=custom-dev
export DEV_BUCKET_PREFIX=custom-dev
export DEV_WEBSOCKET_URI_HOST=dev-websocket.example.com
export DEV_WEBSOCKET_URI_PORT=8545
```

#### Production Environment Variables
```bash
export PROD_AWS_REGION=ap-northeast-1
export PROD_AWS_ACCESS_KEY=your-prod-access-key
export PROD_AWS_SECRET_KEY=your-prod-secret-key
export PROD_S3_BUCKET_NAME=my-prod-bucket
export PROD_EVENTS_TABLE_NAME=custom-prod-Events
export PROD_BLOCK_HEIGHT_TABLE_NAME=custom-prod-BlockHeight
export PROD_TABLE_PREFIX=custom-prod
export PROD_BUCKET_PREFIX=custom-prod
export PROD_WEBSOCKET_URI_HOST=prod-websocket.example.com
```

## Configuration Priority

The configuration loading follows this priority order:

1. **@DynamicPropertySource** (Highest - test-specific overrides)
2. **Environment Variables** (Medium - runtime configuration)
3. **application-{profile}.properties** (Low - profile defaults)
4. **application.properties** (Lowest - global defaults)

## Configuration Files Structure

### application-local.properties
- LocalStack endpoints
- Local development settings
- Fast configuration for local testing

### application-dev.properties
- Real AWS endpoints
- Development-specific table prefixes (`dev-`)
- Development environment URLs

### application-test.properties
- LocalStack endpoints for testing
- Optimized settings for test execution
- Faster check intervals

### application-prod.properties
- Production AWS endpoints
- Production-specific table prefixes (`prod-`)
- Production environment URLs

## Test Classes

### Environment-Specific Test Classes

1. **ConfigurationServiceITSpec** - Tests with `@ActiveProfiles("test")`
2. **DevEnvironmentConfigurationITSpec** - Tests with `@ActiveProfiles("dev")`

### Adding New Environment Tests

To create tests for a new environment:

```groovy
@SpringBootTest(
    classes = [BcmonitoringApplication.class],
    webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("your-environment")
class YourEnvironmentConfigurationITSpec extends BaseAdhocITSpec {
    // Your test methods
}
```

## Environment Configuration Manager

The `EnvironmentConfigManager` class provides utilities for:

- Detecting target environment from system properties/environment variables
- Configuring environment-specific properties
- Managing Spring profile activation
- Loading environment-specific constants (bucket names, table names)
- Debugging environment configuration

### Key Methods

```groovy
// Get target environment (test.environment property or TEST_ENVIRONMENT env var)
String env = EnvironmentConfigManager.getTargetEnvironment()

// Configure properties for specific environment
EnvironmentConfigManager.configureEnvironmentProperties(registry, env, localStackPort)

// Get environment-specific constants
Map<String, String> constants = EnvironmentConfigManager.getEnvironmentConstants(env)

// Print environment info for debugging
EnvironmentConfigManager.printEnvironmentInfo(env)
```

### Environment-Specific Constants

The system automatically loads environment-specific constants:

#### Local Environment
```groovy
TEST_BUCKET = "abijson-local-bucket"
EVENTS_TABLE = "local-Events"
BLOCK_HEIGHT_TABLE = "local-BlockHeight"
TABLE_PREFIX = "local"
BUCKET_PREFIX = "local"
```

#### Test Environment
```groovy
TEST_BUCKET = "abijson-local-bucket"
EVENTS_TABLE = "local-Events"
BLOCK_HEIGHT_TABLE = "local-BlockHeight"
TABLE_PREFIX = "" (empty - no prefix for tables)
BUCKET_PREFIX = "test"
```

#### Development Environment
```groovy
TEST_BUCKET = "abijson-dev-bucket" (or DEV_S3_BUCKET_NAME env var)
EVENTS_TABLE = "dev-Events" (or DEV_EVENTS_TABLE_NAME env var)
BLOCK_HEIGHT_TABLE = "dev-BlockHeight" (or DEV_BLOCK_HEIGHT_TABLE_NAME env var)
TABLE_PREFIX = "dev" (or DEV_TABLE_PREFIX env var)
BUCKET_PREFIX = "dev" (or DEV_BUCKET_PREFIX env var)
```

#### Production Environment
```groovy
TEST_BUCKET = "abijson-prod-bucket" (or PROD_S3_BUCKET_NAME env var)
EVENTS_TABLE = "prod-Events" (or PROD_EVENTS_TABLE_NAME env var)
BLOCK_HEIGHT_TABLE = "prod-BlockHeight" (or PROD_BLOCK_HEIGHT_TABLE_NAME env var)
TABLE_PREFIX = "prod" (or PROD_TABLE_PREFIX env var)
BUCKET_PREFIX = "prod" (or PROD_BUCKET_PREFIX env var)
```

### Prefix Application

The system automatically applies prefixes to resource names:

#### DynamoDB Tables
- **With prefix**: `{TABLE_PREFIX}-{TABLE_NAME}` (e.g., `dev-local-Events`)
- **Without prefix**: `{TABLE_NAME}` (e.g., `local-Events`)

#### S3 Buckets
- **With prefix**: `{BUCKET_PREFIX}-{BUCKET_NAME}` (e.g., `dev-abijson-dev-bucket`)
- **Without prefix**: `{BUCKET_NAME}` (e.g., `abijson-local-bucket`)

## Debugging

### Enable Debug Output

The system automatically prints environment configuration when tests start:

```
=== Environment Configuration ===
Target Environment: dev
Spring Profile: dev
System Property 'test.environment': dev
Environment Variable 'TEST_ENVIRONMENT': null
================================
```

### Common Issues

1. **Wrong environment detected**: Check system property `test.environment` and environment variable `TEST_ENVIRONMENT`
2. **Configuration not loading**: Verify the correct `@ActiveProfiles` annotation
3. **LocalStack not starting**: Ensure Docker is running for local/test environments

## Examples

### Running Tests for Different Scenarios

```bash
# Test local development setup
./scripts/run-tests-with-environment.sh local

# Test against development AWS environment
DEV_AWS_REGION=us-west-2 DEV_S3_BUCKET_NAME=my-dev-bucket ./scripts/run-tests-with-environment.sh dev

# Test configuration loading
./scripts/run-tests-with-environment.sh test ConfigurationServiceITSpec

# Test dev environment configuration
./scripts/run-tests-with-environment.sh dev DevEnvironmentConfigurationITSpec
```

This environment-based configuration system provides flexibility to test against different environments while maintaining isolation and proper configuration management.
