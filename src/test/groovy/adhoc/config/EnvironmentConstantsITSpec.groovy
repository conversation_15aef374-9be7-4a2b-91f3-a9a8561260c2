package adhoc.config

import adhoc.base.BaseAdhocITSpec
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest(
classes = [BcmonitoringApplication.class],
webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("test")
class EnvironmentConstantsITSpec extends BaseAdhocITSpec {

	@Autowired
	Web3jConfig web3jConfig

	@Override
	Web3jConfig getWeb3jConfig() {
		return web3jConfig
	}

	@Autowired
	BcmonitoringConfigurationProperties properties

	def setupSpec() {
		setupSpecCommon()
	}

	def cleanupSpec() {
		cleanupSpecCommon()
	}

	def setup() {
		setupCommon()
	}

	def cleanup() {
		cleanupCommon()
	}

	/**
	 * Test that environment constants are properly initialized
	 * Verifies that TEST_BUCKET, EVENTS_TABLE, BLOCK_HEIGHT_TABLE, prefixes are not null
	 * Expected: All constants should be initialized with environment-specific values
	 */
	def "Should initialize environment constants correctly"() {
		when: "Environment constants are checked"
		// Constants are initialized during @DynamicPropertySource execution

		then: "All constants should be initialized"
		TEST_BUCKET != null
		EVENTS_TABLE != null
		BLOCK_HEIGHT_TABLE != null
		TABLE_PREFIX != null
		BUCKET_PREFIX != null

		and: "Constants should not be empty (except TABLE_PREFIX for test environment)"
		!TEST_BUCKET.isEmpty()
		!EVENTS_TABLE.isEmpty()
		!BLOCK_HEIGHT_TABLE.isEmpty()
		!BUCKET_PREFIX.isEmpty()

		and: "Constants should match test environment values"
		TEST_BUCKET == "abijson-local-bucket"
		EVENTS_TABLE == "local-Events"
		BLOCK_HEIGHT_TABLE == "local-BlockHeight"
		TABLE_PREFIX == "" // Test environment uses empty table prefix
		BUCKET_PREFIX == "test"
	}

	/**
	 * Test that configuration properties match environment constants
	 * Verifies that Spring configuration uses the same values as test constants
	 * Expected: Configuration should be consistent with constants
	 */
	def "Should have configuration properties matching environment constants"() {
		when: "Configuration and constants are compared"
		// Both are loaded during application startup

		then: "Configuration should match constants"
		properties.getAws().getS3().getBucketName() == TEST_BUCKET
		properties.getAws().getDynamodb().getEventsTableName() == EVENTS_TABLE
		properties.getAws().getDynamodb().getBlockHeightTableName() == BLOCK_HEIGHT_TABLE

		and: "Environment should be consistent"
		properties.getEnv() == "local" // Test profile uses local environment
	}

	/**
	 * Test EnvironmentConfigManager utility methods
	 * Verifies that utility methods return correct values for test environment
	 * Expected: Utility methods should work correctly
	 */
	def "Should provide correct environment information via EnvironmentConfigManager"() {
		when: "Environment manager methods are called"
		def targetEnv = EnvironmentConfigManager.getTargetEnvironment()
		def constants = EnvironmentConfigManager.getEnvironmentConstants(targetEnv)
		def profile = EnvironmentConfigManager.getSpringProfile(targetEnv)

		then: "Environment manager should return correct values"
		targetEnv == "test"
		profile == "test"

		and: "Constants should match expected values"
		constants.testBucket == "abijson-local-bucket"
		constants.eventsTable == "local-Events"
		constants.blockHeightTable == "local-BlockHeight"
		constants.tablePrefix == ""
		constants.bucketPrefix == "test"

		and: "Constants should match actual initialized values"
		constants.testBucket == TEST_BUCKET
		constants.eventsTable == EVENTS_TABLE
		constants.blockHeightTable == BLOCK_HEIGHT_TABLE
		constants.tablePrefix == TABLE_PREFIX
		constants.bucketPrefix == BUCKET_PREFIX
	}

	/**
	 * Test that different environments would have different constants
	 * Verifies that EnvironmentConfigManager returns different values for different environments
	 * Expected: Each environment should have its own constants
	 */
	def "Should provide different constants for different environments"() {
		when: "Constants are retrieved for different environments"
		def localConstants = EnvironmentConfigManager.getEnvironmentConstants("local")
		def devConstants = EnvironmentConfigManager.getEnvironmentConstants("dev")
		def testConstants = EnvironmentConfigManager.getEnvironmentConstants("test")
		def prodConstants = EnvironmentConfigManager.getEnvironmentConstants("prod")

		then: "Local and test should have same values (both use LocalStack)"
		localConstants.testBucket == testConstants.testBucket
		localConstants.eventsTable == testConstants.eventsTable
		localConstants.blockHeightTable == testConstants.blockHeightTable

		and: "Dev should have different values"
		devConstants.testBucket == "abijson-dev-bucket"
		devConstants.eventsTable == "dev-Events"
		devConstants.blockHeightTable == "dev-BlockHeight"

		and: "Prod should have different values"
		prodConstants.testBucket == "abijson-prod-bucket"
		prodConstants.eventsTable == "prod-Events"
		prodConstants.blockHeightTable == "prod-BlockHeight"

		and: "All environments should have different bucket names (except local/test)"
		localConstants.testBucket != devConstants.testBucket
		devConstants.testBucket != prodConstants.testBucket
		localConstants.testBucket != prodConstants.testBucket

		and: "All environments should have appropriate prefixes"
		localConstants.tablePrefix == "local"
		devConstants.tablePrefix == "dev"
		testConstants.tablePrefix == ""
		prodConstants.tablePrefix == "prod"
	}

	/**
	 * Test that prefixes are correctly applied to resource names
	 * Verifies that table and bucket names are constructed correctly with prefixes
	 * Expected: Resource names should include environment prefixes
	 */
	def "Should apply prefixes correctly to resource names"() {
		when: "Resource names are constructed with prefixes"
		def eventsTableName = TABLE_PREFIX ? "${TABLE_PREFIX}-${EVENTS_TABLE}" : EVENTS_TABLE
		def blockHeightTableName = TABLE_PREFIX ? "${TABLE_PREFIX}-${BLOCK_HEIGHT_TABLE}" : BLOCK_HEIGHT_TABLE
		def bucketName = BUCKET_PREFIX ? "${BUCKET_PREFIX}-${TEST_BUCKET}" : TEST_BUCKET

		then: "Resource names should be constructed correctly for test environment"
		eventsTableName == "local-Events" // TABLE_PREFIX is empty for test, so no prefix added
		blockHeightTableName == "local-BlockHeight" // TABLE_PREFIX is empty for test, so no prefix added
		bucketName == "test-abijson-local-bucket" // BUCKET_PREFIX is "test"

		and: "Prefixes should be applied correctly"
		TABLE_PREFIX == ""
		BUCKET_PREFIX == "test"
	}
}
